from io import BytesIO
from requests_toolbelt import MultipartEncoder
import requests
from src.models.user import UserTenantDB

from src.reply.minio_client import MinIOClient

from minio import Minio
# Let's say you already have the image as bytes
async def upload_to_sociair(media_url: list,current_user: UserTenantDB) -> dict:
    """
    Uploads a file to Sociair's media upload endpoint.
    
    :param file_bytes: The bytes of the file to be uploaded.
    :return: The response from the upload request.
    """
    # media_url is the mono objectnames get its bytes
    minio_Client = MinIOClient(
        **current_user.minio_config
    )
    for media in media_url:
        # Get the file bytes from MinIO
        file_bytes = await minio_Client.get_object_bytes(
            bucket_name=current_user.minio_config.get("bucket_name"),
            object_name=media
        )

    # Construct the multipart form
    multipart_data = MultipartEncoder(
    fields={
        'uploaded_file[0]': ('Screenshot.png', , 'image/png'),
        'upload_for': 'media'
    }
)  
    access_token_doc = await current_user.async_db.settings.find_one(
    {"name": "sociar_env"},
    {"API_TOKEN": 1, "BASE_URL": 1, "_id": 0}
)

    headers = {
            # 'Authorization': 'Bearer *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
        'Authorization': f'Bearer {access_token_doc.get("API_TOKEN")}',
        'Origin': 'https://techspire.sociair.io',
        'Referer': 'https://techspire.sociair.io/',
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': multipart_data.content_type
    }

    response = requests.post(
        f"{access_token_doc.get('BASE_URL')}/master/medias/upload",
        headers=headers,
        data=multipart_data
    )


    response = response.json()

    return response



async def upload_to_sociair_bytes(file_bytes: bytes, current_user: UserTenantDB) -> dict:
    """
    Uploads a file to Sociair's media upload endpoint.
    
    :param file_bytes: The bytes of the file to be uploaded.
    :return: The response from the upload request.
    """
    # Create a BytesIO object from the file bytes
    file_like = BytesIO(file_bytes)
    
    # Set the file pointer to the beginning
    file_like.seek(0)
    
    # Construct the multipart form
    multipart_data = MultipartEncoder(
    fields={
        'uploaded_file[0]': ('Screenshot.png', file_like, 'image/png'),
        'upload_for': 'media'
    }
)  
    access_token_doc = await current_user.async_db.settings.find_one(
    {"name": "sociar_env"},
    {"API_TOKEN": 1, "BASE_URL": 1, "_id": 0}
)

    headers = { 
        'Authorization': f'Bearer {access_token_doc.get("API_TOKEN")}',
        'Origin': 'https://techspire.sociair.io',
        'Referer': 'https://techspire.sociair.io/',
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': multipart_data.content_type
    }

    response = requests.post(
        f"{access_token_doc.get('BASE_URL')}/master/medias/upload",
        headers=headers,
        data=multipart_data
    )

    response = response.json()

    return response