
from datetime import datetime
from bson import ObjectId
from fastapi import APIRouter, Depends, HTTPException, Body,status
from typing import Optional
from src.core.security import get_tenant_info
from src.models.user import UserTenantDB
from src.helper.logger import setup_new_logging
from src.v2.external_hooks.social_media_webhooks.client import get_sociar_client
from src.v2.external_hooks.social_media_webhooks.models import (
    SocialMediaMessageRequest,
    SocialMediaResponse
)
from src.models.credit import CreditManager, get_credit_info
import json


loggers = setup_new_logging(__name__)
router = APIRouter(tags=["Sociar APIs"], prefix="/sociar-media")

@router.post("/message-send")
async def social_media_message_send(
    request: SocialMediaMessageRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)
) -> SocialMediaResponse:
    """
    Unified endpoint to send messages across different social media platforms.

    Supports: facebook, instagram, whatsapp
    """
    try:
        credit_manager = CreditManager(current_user.db)
        per_cost, remaining_credit = get_credit_info(cost_type="whatsapp_msg_cost", current_user=current_user)
        if remaining_credit < per_cost:
            raise HTTPException(status_code=402, detail=f"Insufficient credits. Required: {per_cost}, Available: {remaining_credit}")
        
        client = await get_sociar_client(current_user)
        latest_entry = await current_user.async_db.ai_response.find_one(
            {"request.user_id": request.user_id}, 
            sort=[("created_at", -1)]
        )
        # First try to find by _id
        customer = await current_user.async_db.customer.find_one(
            {"_id": request.user_id},
            {"customer_name": 1}
        )

        # If not found, try profile_id
        if not customer:
            customer = await current_user.async_db.customer.find_one(
                {"profile_id": request.user_id},
                {"customer_name": 1}
            )

        # Extract username if found
        customer_username = customer.get("customer_username") if customer else None
        if latest_entry and "request" in latest_entry:
            conversation_id = latest_entry["request"].get("conversation_id")
        else:
            conversation_id = None

        
        media_links = " ".join([item.url for item in request.media_url]) if request.media_url else ""
        message_content=request.content + " " + media_links
        # Validate channel
        channel = request.channel.lower()
        if channel not in ["facebook", "instagram", "whatsapp"]:
            return SocialMediaResponse(
                success=False,
                status_code=400,
                message=f"Unsupported channel: {request.channel}. Supported channels: facebook, instagram, whatsapp"
            )
        

        sociar_media_ids=await upload_to_sociair(
            request.media_url,
            current_user
        )
        if channel == "facebook":
            try:
                response = client.send_facebook_message(
                    conversation_id=int(conversation_id),
                    message=message_content

                )
            except Exception as e:
                loggers.error(f"Facebook message error: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, 
                    detail={
                        "success": False,
                        "message": "Failed to send Facebook message. Meta dont allow to send Facebook message if user's message is 7 day old",
                    }
                )
                


        elif channel == "instagram":
            try:
                response = client.send_instagram_message(
                    conversation_id=int(conversation_id),
                    message=message_content
                )
            except Exception as e:
                loggers.error(f"instagram message error: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, 
                    detail={
                        "success": False,
                        "message": "Failed to send Instagram message. Meta dont allow to send Instagram message if user's message is 1 day old",
                    }
                )
        elif channel == "whatsapp":
            try:
                # media_links = " ".join([item.url for item in request.media_url]) if request.media_url else ""
                response = client.send_whatsapp_message(
                    conversation_id=int(conversation_id),
                    message=message_content
                )
            except Exception as e:
                loggers.error(f"WhatsApp message error: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, 
                    detail={
                        "success": False,
                        "message": "Failed to send WhatsApp message. Meta dont allow to send whatsapp message if user's message is 1 day old",
                    }
                )
        credit_result = credit_manager.deduct_credits(
                amount=per_cost,
                description="Whatsapp Message Reply",
                message_id=conversation_id,
            )
        if not credit_result["success"]:
            loggers.error(f"Failed to deduct credits send reply: {credit_result['message']}")

        # Create agent reply document
        agent_reply = {
            "role": "assistant",
            "content": request.content,
            "sender": current_user.user.id,
            "created_at": datetime.now(),
            "user_id": request.user_id,
            "media_values": "",
            "chat_ids": None,
            "message_id": None,
            "image_process_metadata": None,
            "ai_enabled": True,
            "has_credit": True,
            "media_ids": [],
            "verified_by": None,
            "verified_at": None,
            "verified": True,
        }
        
        # Add media IDs if available
        if hasattr(request, "media_url") and request.media_url:
            agent_reply["media_ids"] = [item.name for item in request.media_url]


        chat_message_result = current_user.db.chat_messages.insert_one(agent_reply)
        request_time = datetime.now()
        
        # Create agent message for chat_data
        agent_message = {
            "role": "user",
            "content": request.content if hasattr(request, "user_message") else "",
            "sender": "unknown",
            "created_at": request_time,
            "user_id": request.user_id,
            "media_values": None,
            "chat_ids": None,
            "message_id": None,
            "image_process_metadata": None,
            "ai_enabled": True,
            "has_credit": True,
            "media_ids": [],
            "verified_by": None,
            "verified_at": None,
            "verified": True,
            "_id": str(ObjectId())
        }
        
        # Format the request object with default values
        ai_response = {
            "request": {
                "user_name": getattr(request, "user_name", customer_username),
                "user_id": request.user_id,
                "message": "",
                "chat_data": [
                    {
                        "role": "user",
                        "data": {
                            "content": getattr(request, "user_message", ""),
                            "created_at": request_time
                        }
                    }
                ],
                "chat_data_format": "role_data",
                "media_ids": [],
                "media_values": "",
                "mode": "elaborated",
                "channel": channel.capitalize(),
                "message_topic": None,
                "profile_id": None,
                "from_number": None,
                "country_code": None,
                "phone_number": None,
                "email": None,
                "profile": getattr(request, "profile", None),
                "conversation_id": conversation_id
            },
            "response": {
                "request_time": request_time,
                "processing_time": 0,
                "reply": request.content,
                "information_gathering": [],
                "chat_ids": [str(agent_message["_id"]), str(chat_message_result.inserted_id)],
                "chat_data": [agent_message, agent_reply],
                "latest_message": request.content,
                "metadata": [],
                "language": None,
                "background_processing": True,
                "background_processing_completed": False,
                "reply_urls": [],
                "source_nodes": [],
                "call_to_action": [],
                "ai_enabled": True,
                "has_credit": True,
                "usage": {
                    "prompt_tokens": {
                        "input_tokens": {},
                        "output_tokens": {},
                        "lang_usage": None,
                        "identify_prod_usage": None
                    },
                    "image_process_cost": None
                }
            },
            "created_at": request_time
        }
        
        current_user.db.ai_response.insert_one(ai_response)

        current_user.db.chat_messages.update_one(
            {"_id": chat_message_result.inserted_id},
            {"$set": {"message_id": str(ai_response["_id"])}},
        )
        

        return SocialMediaResponse(
            success=True,
            data=response,
            message=f"message sent successfully "
        )

    except HTTPException:
        raise 
    except Exception as e:
        loggers.error(f"Error sending {request.channel} message: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to send message")

@router.post("/whatsapp/setup")
async def setup_sociar_api(
    api_token: str = Body(...),
    base_url: Optional[str] = Body("https://new-central-api.sociair.com/api"),
    current_user: UserTenantDB = Depends(get_tenant_info)
) -> SocialMediaResponse:
    """
    Set up Sociar API configuration in the database.

    Args:
        api_token: API token for authentication
        base_url: Base URL for the API
        current_user: Current user with database access

    Returns:
        Success message
    """
    try:
        # Check if settings already exist
        existing_settings = await current_user.async_db.settings.find_one({"name": "sociar_env"})

        settings = {
            "API_TOKEN": api_token,
            "BASE_URL": base_url
        }

        if existing_settings:
            # Update existing settings
            result = await current_user.async_db.settings.update_one(
                {"name": "sociar_env"},
                {"$set": settings}
            )

            if result.modified_count == 0:
                raise Exception("Failed to update Sociar settings")

            return SocialMediaResponse(
                success=True,
                message="Sociar API settings updated successfully"
            )
        else:
            # Create new settings
            settings["name"] = "sociar_env"

            result = await current_user.async_db.settings.insert_one(settings)

            if not result.inserted_id:
                raise Exception("Failed to create Sociar settings")

            return SocialMediaResponse(
                success=True,
                message="Sociar API settings created successfully"
            )
    except Exception as e:
        loggers.error(f"Error setting up Sociar API: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to set up Sociar API: {str(e)}")



